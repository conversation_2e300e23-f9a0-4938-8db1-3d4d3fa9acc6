# 在QMT内置Python环境中运行
import pandas as pd

def get_financial_data():
    # 直接使用QMT内置函数
    stock_code = '000001.SZ'
    
    # 获取财务数据
    balance_data = get_balance_sheet(stock_code)  # QMT内置函数
    income_data = get_income_statement(stock_code)
    
    # 保存到QMT数据目录
    balance_data.to_csv('D:/QMT/UserData/financial_balance.csv')
    income_data.to_csv('D:/QMT/UserData/financial_income.csv')
    
    return balance_data, income_data

# 在QMT策略中调用
get_financial_data()